## Found a bug?
- Check if the bug has already been reported under under [Issues](https://github.com/communityox/ox_inventory/issues).
- If an **active** issue matches your own, provide additional information on the existing issue.
- If there is no **open** issue related to the bug, create a new issue. Include a **descriptive title and clear description** with as much relevant information as possible, and include **code samples** or **reproduction steps**.
- Use the relevant bug report template when creating an issue.

## Patched a bug?
- Open a new pull request including **only** the related changes.
- Clearly describe the problem being fixed, and the solution. If the patch resolves any issues, mention them in the description.

## Want to share an improvement or add a new feature?
- Create an issue discussing the change and wait for feedback.
- If you've already worked on the change you can submit a **draft** pull request for feedback and review.
- Not all features and changes are desired! Changes may be messy, poorly-planned, incomplete, or simply incompatible with our design philosophy.

## Is your change cosmetic (e.g. formatting)?
- We will not accept pull requests that do not make substantial changes to the stability or functionality of the resource.
